'use client';

import React, { useState } from 'react';
import { Trash2, Alert<PERSON>riangle, X, Loader2 } from 'lucide-react';
import { handleBulkDeleteQuestionsAction } from '@/actions/worksheetQuestion.action';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface BulkDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (deletedIds: string[]) => void;
  worksheetId: string;
  questionIds: string[];
  questionCount: number;
  className?: string;
}

export const BulkDeleteModal: React.FC<BulkDeleteModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  worksheetId,
  questionIds,
  questionCount,
  className
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reason, setReason] = useState('');
  const [forceRemoval, setForceRemoval] = useState(false);

  const handleBulkDelete = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      const response = await handleBulkDeleteQuestionsAction(
        worksheetId,
        questionIds,
        reason || undefined,
        forceRemoval
      );

      if (response.status === 'success') {
        onSuccess(questionIds);
        onClose();
      } else {
        setError(response.message || 'Failed to delete questions');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className={cn(
        "modal-box w-full max-w-md",
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-error/10 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-error" />
            </div>
            <h3 className="text-lg font-semibold">Delete Questions</h3>
          </div>
          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm btn-circle"
            disabled={isDeleting}
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-error mb-4">
            <span>{error}</span>
          </div>
        )}

        {/* Selection Summary */}
        <div className="bg-base-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Trash2 className="w-5 h-5 text-error" />
            <span className="font-medium">
              {questionCount} question{questionCount !== 1 ? 's' : ''} selected
            </span>
          </div>
          <p className="text-sm text-base-content/70">
            You are about to delete {questionCount} question{questionCount !== 1 ? 's' : ''} from this worksheet.
          </p>
        </div>

        {/* Warning Message */}
        <div className="alert alert-warning mb-4">
          <AlertTriangle className="w-5 h-5" />
          <div>
            <h4 className="font-medium">Are you sure?</h4>
            <p className="text-sm">
              This action cannot be undone. All selected questions will be permanently removed from the worksheet.
            </p>
          </div>
        </div>

        {/* Reason Input */}
        <div className="form-control mb-4">
          <label className="label">
            <span className="label-text">Reason for deletion (optional)</span>
          </label>
          <textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="textarea textarea-bordered h-20"
            placeholder="Provide a reason for deleting these questions..."
            disabled={isDeleting}
            maxLength={500}
          />
          <label className="label">
            <span className="label-text-alt">{reason.length}/500 characters</span>
          </label>
        </div>

        {/* Force Removal Option */}
        <div className="form-control mb-6">
          <label className="label cursor-pointer">
            <span className="label-text">
              <div>
                <div className="font-medium">Force removal</div>
                <div className="text-sm text-base-content/70">
                  Allow deletion even if it would leave the worksheet empty
                </div>
              </div>
            </span>
            <input
              type="checkbox"
              className="checkbox checkbox-warning"
              checked={forceRemoval}
              onChange={(e) => setForceRemoval(e.target.checked)}
              disabled={isDeleting}
            />
          </label>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2">
          <Button
            variant="ghost"
            onClick={onClose}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            onClick={handleBulkDelete}
            disabled={isDeleting}
            className="btn-error"
          >
            {isDeleting && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
            <Trash2 className="w-4 h-4 mr-2" />
            Delete {questionCount} Question{questionCount !== 1 ? 's' : ''}
          </Button>
        </div>
      </div>
    </div>
  );
};
