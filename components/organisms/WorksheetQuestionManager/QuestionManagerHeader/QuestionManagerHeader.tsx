'use client';

import React from 'react';
import { Plus, Trash2, CheckSquare, Square } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface QuestionManagerHeaderProps {
  totalQuestions: number;
  selectedCount: number;
  canAddQuestion: boolean;
  canBulkOperations: boolean;
  onAddQuestion: () => void;
  onBulkDelete: () => void;
  onSelectAll: (selected: boolean) => void;
  isLoading: boolean;
  className?: string;
}

export const QuestionManagerHeader: React.FC<QuestionManagerHeaderProps> = ({
  totalQuestions,
  selectedCount,
  canAddQuestion,
  canBulkOperations,
  onAddQuestion,
  onBulkDelete,
  onSelectAll,
  isLoading,
  className
}) => {
  const allSelected = selectedCount === totalQuestions && totalQuestions > 0;
  const someSelected = selectedCount > 0;

  return (
    <div className={cn(
      "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 bg-base-100 border-b border-base-300",
      className
    )}>
      {/* Left side - Title and stats */}
      <div className="flex flex-col gap-1">
        <h2 className="text-xl font-semibold text-base-content">
          Questions ({totalQuestions})
        </h2>
        {someSelected && (
          <p className="text-sm text-base-content/70">
            {selectedCount} of {totalQuestions} selected
          </p>
        )}
      </div>

      {/* Right side - Actions */}
      <div className="flex flex-wrap items-center gap-2">
        {/* Bulk selection controls */}
        {canBulkOperations && totalQuestions > 0 && (
          <div className="flex items-center gap-2">
            <button
              onClick={() => onSelectAll(!allSelected)}
              className="btn btn-ghost btn-sm"
              disabled={isLoading}
              aria-label={allSelected ? "Deselect all questions" : "Select all questions"}
            >
              {allSelected ? (
                <CheckSquare className="w-4 h-4" />
              ) : (
                <Square className="w-4 h-4" />
              )}
              <span className="hidden sm:inline">
                {allSelected ? 'Deselect All' : 'Select All'}
              </span>
            </button>

            {/* Bulk delete button */}
            {someSelected && (
              <Button
                variant="outline"
                size="sm"
                onClick={onBulkDelete}
                disabled={isLoading}
                className="btn-error"
              >
                <Trash2 className="w-4 h-4" />
                <span className="hidden sm:inline">
                  Delete ({selectedCount})
                </span>
                <span className="sm:hidden">
                  Delete
                </span>
              </Button>
            )}
          </div>
        )}

        {/* Add question button */}
        {canAddQuestion && (
          <Button
            variant="primary"
            size="sm"
            onClick={onAddQuestion}
            disabled={isLoading}
            className="btn-primary"
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline">Add Question</span>
            <span className="sm:hidden">Add</span>
          </Button>
        )}
      </div>
    </div>
  );
};
